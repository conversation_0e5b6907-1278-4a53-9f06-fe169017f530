<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res"><file name="button_press" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\button_press.xml" qualifiers="" type="anim"/><file name="button_press_feedback" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\button_press_feedback.xml" qualifiers="" type="anim"/><file name="button_release_feedback" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\button_release_feedback.xml" qualifiers="" type="anim"/><file name="comment_like_animation" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\comment_like_animation.xml" qualifiers="" type="anim"/><file name="comment_send_success" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\comment_send_success.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="item_animation_comment_fade_in" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_comment_fade_in.xml" qualifiers="" type="anim"/><file name="item_animation_comment_slide_up" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_comment_slide_up.xml" qualifiers="" type="anim"/><file name="item_animation_fall_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_fall_down.xml" qualifiers="" type="anim"/><file name="item_animation_from_bottom" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_from_bottom.xml" qualifiers="" type="anim"/><file name="item_animation_from_right" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\item_animation_from_right.xml" qualifiers="" type="anim"/><file name="layout_animation_comment_list" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\layout_animation_comment_list.xml" qualifiers="" type="anim"/><file name="layout_animation_fall_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\layout_animation_fall_down.xml" qualifiers="" type="anim"/><file name="page_transition_fade" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\page_transition_fade.xml" qualifiers="" type="anim"/><file name="reset_rotation" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\reset_rotation.xml" qualifiers="" type="anim"/><file name="rotate" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate.xml" qualifiers="" type="anim"/><file name="rotate_album" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_album.xml" qualifiers="" type="anim"/><file name="rotate_pause_to_play" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_pause_to_play.xml" qualifiers="" type="anim"/><file name="rotate_play_to_pause" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\rotate_play_to_pause.xml" qualifiers="" type="anim"/><file name="scale_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\scale_down.xml" qualifiers="" type="anim"/><file name="scale_up" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\scale_up.xml" qualifiers="" type="anim"/><file name="shake" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\shake.xml" qualifiers="" type="anim"/><file name="slide_in_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_in_down.xml" qualifiers="" type="anim"/><file name="slide_in_left" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_in_up" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_in_up.xml" qualifiers="" type="anim"/><file name="slide_out_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_out_down.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="slide_out_up" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_out_up.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="album_art_border" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\album_art_border.xml" qualifiers="" type="drawable"/><file name="back_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\back_button_background.xml" qualifiers="" type="drawable"/><file name="bg_bottom_sheet" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_bottom_sheet.xml" qualifiers="" type="drawable"/><file name="bg_button" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_button.xml" qualifiers="" type="drawable"/><file name="bg_button_primary" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_button_primary.xml" qualifiers="" type="drawable"/><file name="bg_comment_input" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_comment_input.xml" qualifiers="" type="drawable"/><file name="bg_comment_success" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_comment_success.xml" qualifiers="" type="drawable"/><file name="bg_edit_text" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_edit_text.xml" qualifiers="" type="drawable"/><file name="bg_playing_cover_border" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_cover_border.xml" qualifiers="" type="drawable"/><file name="bg_playing_disc" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_disc.xml" qualifiers="" type="drawable"/><file name="bg_playing_playback_progress" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_playing_playback_progress.xml" qualifiers="" type="drawable"/><file name="bg_vip_tag" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\bg_vip_tag.xml" qualifiers="" type="drawable"/><file name="button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_primary" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_rounded" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_rounded.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\cherry_blossom_car.jpg" qualifiers="" type="drawable"/><file name="cherry_blossom_car_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\cherry_blossom_car_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_top3" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\circle_background_top3.xml" qualifiers="" type="drawable"/><file name="comment_input_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\comment_input_background.xml" qualifiers="" type="drawable"/><file name="control_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\control_button_background.xml" qualifiers="" type="drawable"/><file name="custom_app_icon" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\custom_app_icon.xml" qualifiers="" type="drawable"/><file name="dark_blue_gradient_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\dark_blue_gradient_background.xml" qualifiers="" type="drawable"/><file name="default_album_art" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\default_album_art.xml" qualifiers="" type="drawable"/><file name="default_avatar" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\default_avatar.xml" qualifiers="" type="drawable"/><file name="default_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\default_background.xml" qualifiers="" type="drawable"/><file name="default_cover" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\default_cover.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="gradient_blue_purple" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\gradient_blue_purple.xml" qualifiers="" type="drawable"/><file name="gradient_overlay" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\gradient_overlay.xml" qualifiers="" type="drawable"/><file name="icon_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_car.xml" qualifiers="" type="drawable"/><file name="icon_globe" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_globe.xml" qualifiers="" type="drawable"/><file name="icon_heart" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_heart.xml" qualifiers="" type="drawable"/><file name="icon_music" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_music.xml" qualifiers="" type="drawable"/><file name="icon_settings" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_settings.xml" qualifiers="" type="drawable"/><file name="icon_user" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\icon_user.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_down" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_down.xml" qualifiers="" type="drawable"/><file name="ic_arrow_left" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_left.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_back" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_back.xml" qualifiers="" type="drawable"/><file name="ic_back_improved" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_back_improved.xml" qualifiers="" type="drawable"/><file name="ic_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_car.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_comment" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_comment.xml" qualifiers="" type="drawable"/><file name="ic_default_cover" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_default_cover.xml" qualifiers="" type="drawable"/><file name="ic_discovery" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_discovery.xml" qualifiers="" type="drawable"/><file name="ic_driving" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_driving.xml" qualifiers="" type="drawable"/><file name="ic_equalizer" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_equalizer.xml" qualifiers="" type="drawable"/><file name="ic_favorite" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_favorite.xml" qualifiers="" type="drawable"/><file name="ic_favorite_border" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_favorite_border.xml" qualifiers="" type="drawable"/><file name="ic_globe" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_globe.xml" qualifiers="" type="drawable"/><file name="ic_grid" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_grid.xml" qualifiers="" type="drawable"/><file name="ic_guest_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_guest_login.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_heart_mode" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_heart_mode.xml" qualifiers="" type="drawable"/><file name="ic_intelligence" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_intelligence.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_library" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_library.xml" qualifiers="" type="drawable"/><file name="ic_like_filled" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_like_filled.xml" qualifiers="" type="drawable"/><file name="ic_like_outline" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_like_outline.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_music" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_music.xml" qualifiers="" type="drawable"/><file name="ic_music_note" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_music_note.xml" qualifiers="" type="drawable"/><file name="ic_needle" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_needle.xml" qualifiers="" type="drawable"/><file name="ic_next" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_next.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_phone_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_phone_login.xml" qualifiers="" type="drawable"/><file name="ic_play" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_player" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_player.xml" qualifiers="" type="drawable"/><file name="ic_playing_needle" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playing_needle.xml" qualifiers="" type="drawable"/><file name="ic_playing_playback_progress_thumb" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playing_playback_progress_thumb.xml" qualifiers="" type="drawable"/><file name="ic_playing_play_pause_selector" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playing_play_pause_selector.xml" qualifiers="" type="drawable"/><file name="ic_playlist" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_playlist.xml" qualifiers="" type="drawable"/><file name="ic_play_mode_level_list" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_level_list.xml" qualifiers="" type="drawable"/><file name="ic_play_mode_loop" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_loop.xml" qualifiers="" type="drawable"/><file name="ic_play_mode_shuffle" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_shuffle.xml" qualifiers="" type="drawable"/><file name="ic_play_mode_single" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_mode_single.xml" qualifiers="" type="drawable"/><file name="ic_play_order" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_order.xml" qualifiers="" type="drawable"/><file name="ic_play_small" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_play_small.xml" qualifiers="" type="drawable"/><file name="ic_previous" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_previous.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_qrcode_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_qrcode_login.xml" qualifiers="" type="drawable"/><file name="ic_qr_error" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_qr_error.xml" qualifiers="" type="drawable"/><file name="ic_qr_placeholder" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_qr_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_random" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_random.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_repeat" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_repeat.xml" qualifiers="" type="drawable"/><file name="ic_repeat_all" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_repeat_all.xml" qualifiers="" type="drawable"/><file name="ic_repeat_one" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_repeat_one.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_shuffle" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_shuffle.xml" qualifiers="" type="drawable"/><file name="ic_user" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_user.xml" qualifiers="" type="drawable"/><file name="ic_vinyl_bg" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_vinyl_bg.xml" qualifiers="" type="drawable"/><file name="login_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\login_button_background.xml" qualifiers="" type="drawable"/><file name="logo" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\logo.jpg" qualifiers="" type="drawable"/><file name="logo_music" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\logo_music.png" qualifiers="" type="drawable"/><file name="modern_login_button" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\modern_login_button.xml" qualifiers="" type="drawable"/><file name="nav_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\nav_button_background.xml" qualifiers="" type="drawable"/><file name="next" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\next.png" qualifiers="" type="drawable"/><file name="pause" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\pause.png" qualifiers="" type="drawable"/><file name="play" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\play.png" qualifiers="" type="drawable"/><file name="player_control_bg" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\player_control_bg.xml" qualifiers="" type="drawable"/><file name="previous" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\previous.png" qualifiers="" type="drawable"/><file name="ripple_circular_button" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ripple_circular_button.xml" qualifiers="" type="drawable"/><file name="ripple_oval_button" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ripple_oval_button.xml" qualifiers="" type="drawable"/><file name="rounded_status_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\rounded_status_background.xml" qualifiers="" type="drawable"/><file name="round_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\round_button_background.xml" qualifiers="" type="drawable"/><file name="round_icon_bg" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\round_icon_bg.xml" qualifiers="" type="drawable"/><file name="round_menu_button_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\round_menu_button_background.xml" qualifiers="" type="drawable"/><file name="sakura_button" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sakura_button.xml" qualifiers="" type="drawable"/><file name="sakura_button_secondary" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sakura_button_secondary.xml" qualifiers="" type="drawable"/><file name="sakura_dialog_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sakura_dialog_background.xml" qualifiers="" type="drawable"/><file name="sakura_edit_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sakura_edit_background.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="sidebar_sakura_bg" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\sidebar_sakura_bg.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="splash_gradient" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\splash_gradient.xml" qualifiers="" type="drawable"/><file name="vinyl_arm" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\vinyl_arm.xml" qualifiers="" type="drawable"/><file name="vinyl_border" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\vinyl_border.xml" qualifiers="" type="drawable"/><file name="vinyl_center" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\vinyl_center.xml" qualifiers="" type="drawable"/><file name="vinyl_record" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\vinyl_record.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_player" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_player.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="dialog_comment" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_comment.xml" qualifiers="" type="layout"/><file name="dialog_heart_mode" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_heart_mode.xml" qualifiers="" type="layout"/><file name="dialog_intelligence" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_intelligence.xml" qualifiers="" type="layout"/><file name="dialog_phone_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_phone_login.xml" qualifiers="" type="layout"/><file name="dialog_playlist" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_playlist.xml" qualifiers="" type="layout"/><file name="dialog_qr_login" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\dialog_qr_login.xml" qualifiers="" type="layout"/><file name="fragment_comment" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_comment.xml" qualifiers="" type="layout"/><file name="fragment_driving_mode" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_driving_mode.xml" qualifiers="" type="layout"/><file name="fragment_intelligence" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_intelligence.xml" qualifiers="" type="layout"/><file name="fragment_placeholder" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_placeholder.xml" qualifiers="" type="layout"/><file name="fragment_player" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_player.xml" qualifiers="" type="layout"/><file name="fragment_top_list" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_top_list.xml" qualifiers="" type="layout"/><file name="fragment_user_profile" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\fragment_user_profile.xml" qualifiers="" type="layout"/><file name="item_comment" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment.xml" qualifiers="" type="layout"/><file name="item_comment_header" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_comment_header.xml" qualifiers="" type="layout"/><file name="item_hot_search" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_hot_search.xml" qualifiers="" type="layout"/><file name="item_online_song" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_online_song.xml" qualifiers="" type="layout"/><file name="item_playlist" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist.xml" qualifiers="" type="layout"/><file name="item_playlist_song" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_playlist_song.xml" qualifiers="" type="layout"/><file name="item_reply" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_reply.xml" qualifiers="" type="layout"/><file name="item_search_suggest" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_search_suggest.xml" qualifiers="" type="layout"/><file name="item_song" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_song.xml" qualifiers="" type="layout"/><file name="item_top_list" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\item_top_list.xml" qualifiers="" type="layout"/><file name="page_player_comments" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_comments.xml" qualifiers="" type="layout"/><file name="page_player_lyrics" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_lyrics.xml" qualifiers="" type="layout"/><file name="page_player_playlist" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\page_player_playlist.xml" qualifiers="" type="layout"/><file name="player_controls" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\player_controls.xml" qualifiers="" type="layout"/><file name="view_album_cover" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_album_cover.xml" qualifiers="" type="layout"/><file name="view_lottie_loading" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\layout\view_lottie_loading.xml" qualifiers="" type="layout"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-anydpi-v26\cherry_blossom_car.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-anydpi-v26\cherry_blossom_car_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="cherry_blossom_car_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\cherry_blossom_car_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-mdpi\cherry_blossom_car.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="cherry_blossom_car_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-mdpi\cherry_blossom_car_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-mdpi\cherry_blossom_car_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xhdpi\cherry_blossom_car.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xhdpi\cherry_blossom_car_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xhdpi\cherry_blossom_car_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxhdpi\cherry_blossom_car.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxhdpi\cherry_blossom_car_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxhdpi\cherry_blossom_car_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="cherry_blossom_car" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxxhdpi\cherry_blossom_car.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_foreground" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxxhdpi\cherry_blossom_car_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="cherry_blossom_car_round" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxxhdpi\cherry_blossom_car_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\attrs.xml" qualifiers=""><attr format="color|reference" name="colorBackground"/><attr format="color|reference" name="textColorPrimary"/><attr format="color|reference" name="textColorSecondary"/><declare-styleable name="LottieLoadingView">
        
        <attr format="string" name="lottieAnimationAsset"/>
        
        <attr format="string" name="loadingMessage"/>
        
        <attr format="boolean" name="autoPlay"/>
        
        <attr format="boolean" name="loop"/>
    </declare-styleable></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\colors.xml" qualifiers=""><color name="color_black">#FF000000</color><color name="color_white">#FFFFFFFF</color><color name="color_transparent">#00000000</color><color name="color_gray_50">#FAFAFA</color><color name="color_gray_100">#F5F5F5</color><color name="color_gray_200">#EEEEEE</color><color name="color_gray_300">#E0E0E0</color><color name="color_gray_400">#BDBDBD</color><color name="color_gray_500">#9E9E9E</color><color name="color_gray_600">#757575</color><color name="color_gray_700">#616161</color><color name="color_gray_800">#424242</color><color name="color_gray_900">#212121</color><color name="color_blue_500">#2196F3</color><color name="color_blue_700">#1976D2</color><color name="color_blue_900">#0D47A1</color><color name="color_light_blue_500">#03A9F4</color><color name="color_light_blue_700">#0288D1</color><color name="color_pink_500">#E91E63</color><color name="color_pink_700">#C2185B</color><color name="color_red_500">#F44336</color><color name="color_green_500">#4CAF50</color><color name="color_yellow_500">#FFEB3B</color><color name="color_purple_200">#FFBB86FC</color><color name="color_purple_500">#FF6200EE</color><color name="color_purple_700">#FF3700B3</color><color name="color_teal_200">#FF03DAC5</color><color name="color_teal_700">#FF018786</color><color name="theme_primary">#1E88E5</color><color name="theme_primary_dark">#0D47A1</color><color name="theme_primary_light">#BBDEFB</color><color name="theme_accent">#42A5F5</color><color name="theme_accent_dark">#F50057</color><color name="theme_accent_light">#FF80AB</color><color name="theme_background">#F5F5F5</color><color name="theme_surface">#FFFFFF</color><color name="theme_error">#F44336</color><color name="theme_success">#4CAF50</color><color name="theme_warning">#FFC107</color><color name="theme_info">#2196F3</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="text_disabled">#9E9E9E</color><color name="text_light">#FFFFFF</color><color name="text_dark">#000000</color><color name="text_link">#2196F3</color><color name="ui_divider">#E0E0E0</color><color name="ui_ripple">#80FFFFFF</color><color name="ui_button">#2196F3</color><color name="ui_button_text">#FFFFFF</color><color name="ui_splash_background">#1E3A5F</color><color name="search_background">#F8F9FA</color><color name="search_stroke">#DADCE0</color><color name="nav_background">#000000</color><color name="nav_icon_active">#1E88E5</color><color name="nav_icon_inactive">#787878</color><color name="nav_indicator">#1E88E5</color><color name="sidebar_background">#000000</color><color name="sidebar_item_text">#FFFFFF</color><color name="sidebar_item_icon_normal">#AAAAAA</color><color name="sidebar_item_icon_selected">#2196F3</color><color name="sidebar_item_background_selected">#303030</color><color name="player_background">#121212</color><color name="player_controls">#FFFFFF</color><color name="player_progress_background">#4D4D4D</color><color name="player_progress">#2196F3</color><color name="player_control_background">#EDEDED</color><color name="lyric_background">#212121</color><color name="lyric_highlight">#2196F3</color><color name="lyric_normal">#9E9E9E</color><color name="list_item_background">#FFFFFF</color><color name="list_item_background_selected">#E3F2FD</color><color name="driving_mode_background">#000000</color><color name="driving_mode_text">#FFFFFF</color><color name="driving_mode_controls">#2196F3</color><color name="sakura_primary">#FFBAD3</color><color name="sakura_primary_dark">#FF9CB6</color><color name="sakura_accent">#FE8BC6</color><color name="sakura_background">#FFF5F8</color><color name="sakura_text_primary">#66304D</color><color name="sakura_text_secondary">#976681</color><color name="sakura_divider">#FFE4EE</color><color name="purple_200">@color/color_purple_200</color><color name="purple_500">@color/color_purple_500</color><color name="purple_700">@color/color_purple_700</color><color name="teal_200">@color/color_teal_200</color><color name="teal_700">@color/color_teal_700</color><color name="black">@color/color_black</color><color name="white">@color/color_white</color><color name="transparent">@color/color_transparent</color><color name="gray">@color/color_gray_500</color><color name="light_gray">@color/color_gray_300</color><color name="dark_gray">@color/color_gray_700</color><color name="red">@color/color_red_500</color><color name="green">@color/color_green_500</color><color name="blue">@color/color_blue_500</color><color name="yellow">@color/color_yellow_500</color><color name="primary">@color/theme_primary</color><color name="primary_dark">@color/theme_primary_dark</color><color name="primary_light">@color/theme_primary_light</color><color name="accent">@color/theme_accent</color><color name="accent_dark">@color/theme_accent_dark</color><color name="accent_light">@color/theme_accent_light</color><color name="background">@color/theme_background</color><color name="divider">@color/ui_divider</color><color name="success">@color/theme_success</color><color name="info">@color/theme_info</color><color name="warning">@color/theme_warning</color><color name="error">@color/theme_error</color><color name="primary_color">@color/theme_primary</color><color name="primary_dark_color">@color/theme_primary_dark</color><color name="accent_color">@color/theme_accent</color><color name="background_color">@color/theme_background</color><color name="text_primary_color">@color/text_primary</color><color name="text_secondary_color">@color/text_secondary</color><color name="divider_color">@color/ui_divider</color><color name="button_color">@color/ui_button</color><color name="button_text_color">@color/ui_button_text</color><color name="ripple_color">@color/ui_ripple</color><color name="splash_background">@color/ui_splash_background</color><color name="lyric_background_color">@color/lyric_background</color><color name="lyric_highlight_color">@color/lyric_highlight</color><color name="lyric_normal_color">@color/lyric_normal</color><color name="search_bg_color">@color/search_background</color><color name="search_stroke_color">@color/search_stroke</color><color name="colorPrimary">@color/theme_primary</color><color name="colorPrimaryDark">@color/theme_primary_dark</color><color name="colorAccent">@color/theme_accent</color><color name="colorBackground">@color/theme_background</color><color name="colorTextPrimary">@color/text_primary</color><color name="colorTextSecondary">@color/text_secondary</color><color name="color_background">@color/theme_background</color><color name="background_dark">#121212</color><color name="background_dark_lighter">#1E1E1E</color><color name="toolbar_background">#1A1A1A</color><color name="card_background">#2A2A2A</color></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="text_size_headline">24sp</dimen><dimen name="text_size_title">20sp</dimen><dimen name="text_size_subtitle">18sp</dimen><dimen name="text_size_body">16sp</dimen><dimen name="text_size_caption">14sp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="driving_text_size_title">30sp</dimen><dimen name="driving_text_size_body">24sp</dimen><dimen name="driving_text_size_small">20sp</dimen><dimen name="margin_tiny">2dp</dimen><dimen name="margin_small">4dp</dimen><dimen name="margin_medium">8dp</dimen><dimen name="margin_normal">16dp</dimen><dimen name="margin_large">24dp</dimen><dimen name="margin_xlarge">32dp</dimen><dimen name="margin_xxlarge">48dp</dimen><dimen name="spacing_large">24dp</dimen><dimen name="padding_tiny">2dp</dimen><dimen name="padding_small">4dp</dimen><dimen name="padding_medium">8dp</dimen><dimen name="padding_normal">16dp</dimen><dimen name="padding_large">24dp</dimen><dimen name="padding_xlarge">32dp</dimen><dimen name="corner_radius_small">4dp</dimen><dimen name="corner_radius_medium">8dp</dimen><dimen name="corner_radius_large">16dp</dimen><dimen name="height_button">48dp</dimen><dimen name="height_toolbar">56dp</dimen><dimen name="height_navigation_item">56dp</dimen><dimen name="height_divider">1dp</dimen><dimen name="height_seekbar">24dp</dimen><dimen name="height_list_item">72dp</dimen><dimen name="height_card">180dp</dimen><dimen name="width_sidebar">80dp</dimen><dimen name="width_icon">24dp</dimen><dimen name="width_fab">56dp</dimen><dimen name="width_sidebar_selection_indicator">4dp</dimen><dimen name="image_thumbnail_small">40dp</dimen><dimen name="image_thumbnail_medium">60dp</dimen><dimen name="image_thumbnail_large">80dp</dimen><dimen name="image_cover_small">120dp</dimen><dimen name="image_cover_medium">180dp</dimen><dimen name="image_cover_large">240dp</dimen><dimen name="image_cover_xlarge">320dp</dimen><dimen name="player_control_size_small">40dp</dimen><dimen name="player_control_size_medium">56dp</dimen><dimen name="player_control_size_large">72dp</dimen><dimen name="player_seekbar_height">6dp</dimen><dimen name="player_seekbar_thumb">16dp</dimen><dimen name="driving_control_size">96dp</dimen><dimen name="driving_seekbar_height">12dp</dimen><dimen name="driving_seekbar_thumb">24dp</dimen><integer name="anim_duration_short">150</integer><integer name="anim_duration_medium">300</integer><integer name="anim_duration_long">500</integer></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\splash_theme.xml" qualifiers=""><style name="SplashTheme" parent="Theme.AppCompat.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">VoxTunes AI</string><string name="action_settings">Settings</string><string name="welcome_message">欢迎使用智能语音音乐播放器</string><string name="no_lyrics">暂无歌词</string><string name="search_hint">输入歌曲名或歌手名</string><string name="search">搜索</string><string name="recommend">推荐新歌</string><string name="toplist">音乐排行榜</string><string name="copyright">© 2024 VoxTunes AI</string><string name="app_logo">轻聆 App Logo</string><string name="app_slogan">您的专属智能车载音乐伴侣</string><string name="qr_code_login">扫码登录</string><string name="phone_login">手机号登录</string><string name="guest_login">游客登录</string><string name="version_info">轻聆音乐 v1.0</string><string name="login_successful">登录成功</string><string name="login_failed">登录失败</string><string name="network_error">网络错误</string><string name="phone_number_hint">请输入手机号</string><string name="password_hint">请输入密码</string><string name="login">登录</string><string name="cancel">取消</string><string name="refresh_qr_code">刷新二维码</string><string name="scan_qr_code_tip">请使用网易云音乐APP扫描二维码登录</string><string name="qr_code_expired">二维码已过期，请点击刷新</string><string name="qr_code_scanned">扫描成功，请在手机上确认登录</string><string name="loading">加载中...</string><string name="play_mode_loop">列表循环</string><string name="play_mode_shuffle">随机播放</string><string name="play_mode_single">单曲循环</string><string name="play">播放</string><string name="pause">暂停</string><string name="next">下一首</string><string name="previous">上一首</string><string name="playlist">播放列表</string><string name="favorite">收藏</string><string name="unfavorite">取消收藏</string><string name="heart_mode">心动模式</string><string name="comment">评论</string><string name="setting_auto_play">自动播放</string><string name="setting_night_mode">夜间模式</string><string name="setting_auto_voice_in_driving">驾驶模式自动语音</string><string name="error_timeout">连接超时，请检查网络后重试</string><string name="error_no_network">网络不可用，请检查网络连接</string><string name="error_network">网络错误，请检查网络连接后重试</string><string name="error_auth">登录已过期，请重新登录</string><string name="error_client">客户端错误 (%1$d)，请稍后重试</string><string name="error_server">服务器错误 (%1$d)，请稍后重试</string><string name="error_unknown">未知错误，请稍后重试</string><string name="retry">重试</string><string name="cancel_retry">取消</string><string name="network_unavailable">网络不可用</string><string name="network_restored">网络已恢复</string><string name="back">返回</string><string name="no_comments">暂无评论</string><string name="comment_hint">说点什么吧...</string><string name="send">发送</string><string name="user_avatar">用户头像</string><string name="comment_title">%1$s 的评论</string><string name="comment_empty_hint">评论内容不能为空</string><string name="intelligence_mode">心动模式</string><string name="intelligence_hint">根据当前歌曲推荐的相似歌曲</string><string name="intelligence_recommendation">推荐歌曲</string><string name="no_intelligence_songs">暂无推荐歌曲</string><string name="album_cover">专辑封面</string><string name="vip">VIP</string></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppButton">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/button_text_color</item>
        <item name="android:padding">10dp</item>
        <item name="android:elevation">3dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="SearchEditText">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/search_background</item>
        <item name="android:padding">12dp</item>
        <item name="android:textColorHint">@color/text_secondary_color</item>
        <item name="android:textColor">@color/text_primary_color</item>
        <item name="android:elevation">2dp</item>
    </style><style name="PlayerControlButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:padding">8dp</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:tint">@color/primary_color</item>
    </style><style name="PlayPauseButton" parent="PlayerControlButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:layout_marginEnd">16dp</item>
    </style><style name="LyricTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingExtra">10dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/lyric_normal_color</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.MaterialComponents.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style><style name="BottomSheetStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_bottom_sheet</item>
        <item name="android:elevation">8dp</item>
        <item name="behavior_peekHeight">512dp</item>
    </style></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AIMusicPlayer" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="21">?attr/colorPrimaryVariant</item>
        
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>

        
        <item name="colorBackground">@color/theme_background</item>
        <item name="textColorPrimary">@color/text_primary</item>
        <item name="textColorSecondary">@color/text_secondary</item>
    </style><style name="FullScreenTheme" parent="Theme.MaterialComponents.NoActionBar">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>

        
        <item name="colorBackground">@color/theme_background</item>
        <item name="textColorPrimary">@color/text_primary</item>
        <item name="textColorSecondary">@color/text_secondary</item>
    </style></file><file path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.AIMusicPlayer" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="21">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file name="button_playlist_close" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\button_playlist_close.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\test\Android-Voice-Controlled-Music-Player-main\Android-Voice-Controlled-Music-Player-main\AIMusicPlayer1\app\build\generated\res\resValues\debug"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="LottieLoadingView">
        
        <attr format="string" name="lottieAnimationAsset"/>
        
        <attr format="string" name="loadingMessage"/>
        
        <attr format="boolean" name="autoPlay"/>
        
        <attr format="boolean" name="loop"/>
    </declare-styleable></configuration></mergedItems></merger>