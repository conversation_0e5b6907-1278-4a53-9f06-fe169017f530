package com.example.aimusicplayer;

import android.content.SharedPreferences;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MusicApplication_MembersInjector implements MembersInjector<MusicApplication> {
  private final Provider<SharedPreferences> sharedPreferencesProvider;

  public MusicApplication_MembersInjector(Provider<SharedPreferences> sharedPreferencesProvider) {
    this.sharedPreferencesProvider = sharedPreferencesProvider;
  }

  public static MembersInjector<MusicApplication> create(
      Provider<SharedPreferences> sharedPreferencesProvider) {
    return new MusicApplication_MembersInjector(sharedPreferencesProvider);
  }

  @Override
  public void injectMembers(MusicApplication instance) {
    injectSharedPreferences(instance, sharedPreferencesProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.MusicApplication.sharedPreferences")
  public static void injectSharedPreferences(MusicApplication instance,
      SharedPreferences sharedPreferences) {
    instance.sharedPreferences = sharedPreferences;
  }
}
