<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="349" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="142" endOffset="18"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="41" startOffset="12" endLine="45" endOffset="56"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="48" startOffset="12" endLine="59" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="62" startOffset="12" endLine="70" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="81" startOffset="16" endLine="94" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="109" endOffset="46"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="122" startOffset="12" endLine="132" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="135" startOffset="12" endLine="140" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="145" startOffset="4" endLine="155" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="158" startOffset="4" endLine="348" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="178" startOffset="12" endLine="186" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="188" startOffset="12" endLine="198" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="200" startOffset="12" endLine="208" endOffset="38"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="226" startOffset="16" endLine="236" endOffset="71"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="239" startOffset="16" endLine="249" endOffset="71"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="260" startOffset="20" endLine="266" endOffset="60"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="270" startOffset="16" endLine="280" endOffset="71"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="283" startOffset="16" endLine="293" endOffset="71"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="296" startOffset="16" endLine="306" endOffset="71"/></Target><Target id="@+id/button_player_intelligence" view="ImageView"><Expressions/><location startLine="309" startOffset="16" endLine="319" endOffset="71"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="322" startOffset="16" endLine="332" endOffset="71"/></Target><Target id="@+id/button_player_share" view="ImageView"><Expressions/><location startLine="335" startOffset="16" endLine="345" endOffset="71"/></Target></Targets></Layout>