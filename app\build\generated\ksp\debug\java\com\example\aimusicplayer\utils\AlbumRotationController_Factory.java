package com.example.aimusicplayer.utils;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumRotationController_Factory implements Factory<AlbumRotationController> {
  @Override
  public AlbumRotationController get() {
    return newInstance();
  }

  public static AlbumRotationController_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AlbumRotationController newInstance() {
    return new AlbumRotationController();
  }

  private static final class InstanceHolder {
    private static final AlbumRotationController_Factory INSTANCE = new AlbumRotationController_Factory();
  }
}
