<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\activity_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/activity_player_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="13"/></Target><Target id="@+id/iv_background" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="13" endOffset="40"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="31" startOffset="12" endLine="40" endOffset="49"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="49" startOffset="16" endLine="58" endOffset="45"/></Target><Target id="@+id/tv_artist" view="TextView"><Expressions/><location startLine="60" startOffset="16" endLine="69" endOffset="45"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="80" startOffset="12" endLine="84" endOffset="49"/></Target><Target id="@+id/sv_lyrics" view="ScrollView"><Expressions/><location startLine="87" startOffset="12" endLine="103" endOffset="24"/></Target><Target id="@+id/tv_lyrics" view="TextView"><Expressions/><location startLine="93" startOffset="16" endLine="102" endOffset="45"/></Target><Target id="@+id/tv_current_time" view="TextView"><Expressions/><location startLine="120" startOffset="16" endLine="126" endOffset="45"/></Target><Target id="@+id/seekbar_progress" view="SeekBar"><Expressions/><location startLine="128" startOffset="16" endLine="136" endOffset="82"/></Target><Target id="@+id/tv_total_time" view="TextView"><Expressions/><location startLine="138" startOffset="16" endLine="144" endOffset="45"/></Target><Target id="@+id/btn_play_mode" view="ImageButton"><Expressions/><location startLine="155" startOffset="16" endLine="163" endOffset="53"/></Target><Target id="@+id/btn_prev" view="ImageButton"><Expressions/><location startLine="165" startOffset="16" endLine="173" endOffset="53"/></Target><Target id="@+id/btn_play_pause" view="ImageButton"><Expressions/><location startLine="175" startOffset="16" endLine="183" endOffset="53"/></Target><Target id="@+id/btn_next" view="ImageButton"><Expressions/><location startLine="185" startOffset="16" endLine="193" endOffset="53"/></Target><Target id="@+id/btn_playlist" view="ImageButton"><Expressions/><location startLine="195" startOffset="16" endLine="203" endOffset="53"/></Target></Targets></Layout>