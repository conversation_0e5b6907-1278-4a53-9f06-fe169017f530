package com.example.aimusicplayer.di;

import android.content.Context;
import com.example.aimusicplayer.utils.AlbumArtBlurCache;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideAlbumArtBlurCacheFactory implements Factory<AlbumArtBlurCache> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideAlbumArtBlurCacheFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AlbumArtBlurCache get() {
    return provideAlbumArtBlurCache(contextProvider.get());
  }

  public static AppModule_ProvideAlbumArtBlurCacheFactory create(
      Provider<Context> contextProvider) {
    return new AppModule_ProvideAlbumArtBlurCacheFactory(contextProvider);
  }

  public static AlbumArtBlurCache provideAlbumArtBlurCache(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideAlbumArtBlurCache(context));
  }
}
