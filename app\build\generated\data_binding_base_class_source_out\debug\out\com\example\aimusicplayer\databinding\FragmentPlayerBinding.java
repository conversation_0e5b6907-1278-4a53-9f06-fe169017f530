// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import androidx.viewpager2.widget.ViewPager2;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.widget.AlbumCoverView;
import com.example.aimusicplayer.ui.widget.LottieLoadingView;
import com.google.android.material.tabs.TabLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentPlayerBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView albumArt;

  @NonNull
  public final AlbumCoverView albumCoverView;

  @NonNull
  public final ImageView backgroundBlur;

  @NonNull
  public final View backgroundOverlay;

  @NonNull
  public final ImageView buttonPlayerCollect;

  @NonNull
  public final ImageView buttonPlayerComment;

  @NonNull
  public final ImageView buttonPlayerIntelligence;

  @NonNull
  public final ImageView buttonPlayerNext;

  @NonNull
  public final ImageView buttonPlayerPlayMode;

  @NonNull
  public final ImageView buttonPlayerPlayPause;

  @NonNull
  public final ImageView buttonPlayerPlaylist;

  @NonNull
  public final ImageView buttonPlayerPrev;

  @NonNull
  public final ImageView buttonPlayerShare;

  @NonNull
  public final LinearLayout contentContainer;

  @NonNull
  public final LinearLayout controlContainer;

  @NonNull
  public final LottieLoadingView loadingView;

  @NonNull
  public final SeekBar seekbarPlayerProgress;

  @NonNull
  public final TextView songArtist;

  @NonNull
  public final TextView songTitle;

  @NonNull
  public final TabLayout tabLayoutPlayer;

  @NonNull
  public final TextView textviewPlayerCurrentTime;

  @NonNull
  public final TextView textviewPlayerTotalTime;

  @NonNull
  public final ViewPager2 viewPagerPlayer;

  @NonNull
  public final ImageView vinylBackground;

  private FragmentPlayerBinding(@NonNull RelativeLayout rootView, @NonNull ImageView albumArt,
      @NonNull AlbumCoverView albumCoverView, @NonNull ImageView backgroundBlur,
      @NonNull View backgroundOverlay, @NonNull ImageView buttonPlayerCollect,
      @NonNull ImageView buttonPlayerComment, @NonNull ImageView buttonPlayerIntelligence,
      @NonNull ImageView buttonPlayerNext, @NonNull ImageView buttonPlayerPlayMode,
      @NonNull ImageView buttonPlayerPlayPause, @NonNull ImageView buttonPlayerPlaylist,
      @NonNull ImageView buttonPlayerPrev, @NonNull ImageView buttonPlayerShare,
      @NonNull LinearLayout contentContainer, @NonNull LinearLayout controlContainer,
      @NonNull LottieLoadingView loadingView, @NonNull SeekBar seekbarPlayerProgress,
      @NonNull TextView songArtist, @NonNull TextView songTitle, @NonNull TabLayout tabLayoutPlayer,
      @NonNull TextView textviewPlayerCurrentTime, @NonNull TextView textviewPlayerTotalTime,
      @NonNull ViewPager2 viewPagerPlayer, @NonNull ImageView vinylBackground) {
    this.rootView = rootView;
    this.albumArt = albumArt;
    this.albumCoverView = albumCoverView;
    this.backgroundBlur = backgroundBlur;
    this.backgroundOverlay = backgroundOverlay;
    this.buttonPlayerCollect = buttonPlayerCollect;
    this.buttonPlayerComment = buttonPlayerComment;
    this.buttonPlayerIntelligence = buttonPlayerIntelligence;
    this.buttonPlayerNext = buttonPlayerNext;
    this.buttonPlayerPlayMode = buttonPlayerPlayMode;
    this.buttonPlayerPlayPause = buttonPlayerPlayPause;
    this.buttonPlayerPlaylist = buttonPlayerPlaylist;
    this.buttonPlayerPrev = buttonPlayerPrev;
    this.buttonPlayerShare = buttonPlayerShare;
    this.contentContainer = contentContainer;
    this.controlContainer = controlContainer;
    this.loadingView = loadingView;
    this.seekbarPlayerProgress = seekbarPlayerProgress;
    this.songArtist = songArtist;
    this.songTitle = songTitle;
    this.tabLayoutPlayer = tabLayoutPlayer;
    this.textviewPlayerCurrentTime = textviewPlayerCurrentTime;
    this.textviewPlayerTotalTime = textviewPlayerTotalTime;
    this.viewPagerPlayer = viewPagerPlayer;
    this.vinylBackground = vinylBackground;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentPlayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentPlayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_player, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentPlayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.album_art;
      ImageView albumArt = ViewBindings.findChildViewById(rootView, id);
      if (albumArt == null) {
        break missingId;
      }

      id = R.id.album_cover_view;
      AlbumCoverView albumCoverView = ViewBindings.findChildViewById(rootView, id);
      if (albumCoverView == null) {
        break missingId;
      }

      id = R.id.background_blur;
      ImageView backgroundBlur = ViewBindings.findChildViewById(rootView, id);
      if (backgroundBlur == null) {
        break missingId;
      }

      id = R.id.background_overlay;
      View backgroundOverlay = ViewBindings.findChildViewById(rootView, id);
      if (backgroundOverlay == null) {
        break missingId;
      }

      id = R.id.button_player_collect;
      ImageView buttonPlayerCollect = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerCollect == null) {
        break missingId;
      }

      id = R.id.button_player_comment;
      ImageView buttonPlayerComment = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerComment == null) {
        break missingId;
      }

      id = R.id.button_player_intelligence;
      ImageView buttonPlayerIntelligence = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerIntelligence == null) {
        break missingId;
      }

      id = R.id.button_player_next;
      ImageView buttonPlayerNext = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerNext == null) {
        break missingId;
      }

      id = R.id.button_player_play_mode;
      ImageView buttonPlayerPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerPlayMode == null) {
        break missingId;
      }

      id = R.id.button_player_play_pause;
      ImageView buttonPlayerPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerPlayPause == null) {
        break missingId;
      }

      id = R.id.button_player_playlist;
      ImageView buttonPlayerPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerPlaylist == null) {
        break missingId;
      }

      id = R.id.button_player_prev;
      ImageView buttonPlayerPrev = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerPrev == null) {
        break missingId;
      }

      id = R.id.button_player_share;
      ImageView buttonPlayerShare = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayerShare == null) {
        break missingId;
      }

      id = R.id.content_container;
      LinearLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.control_container;
      LinearLayout controlContainer = ViewBindings.findChildViewById(rootView, id);
      if (controlContainer == null) {
        break missingId;
      }

      id = R.id.loading_view;
      LottieLoadingView loadingView = ViewBindings.findChildViewById(rootView, id);
      if (loadingView == null) {
        break missingId;
      }

      id = R.id.seekbar_player_progress;
      SeekBar seekbarPlayerProgress = ViewBindings.findChildViewById(rootView, id);
      if (seekbarPlayerProgress == null) {
        break missingId;
      }

      id = R.id.song_artist;
      TextView songArtist = ViewBindings.findChildViewById(rootView, id);
      if (songArtist == null) {
        break missingId;
      }

      id = R.id.song_title;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      id = R.id.tab_layout_player;
      TabLayout tabLayoutPlayer = ViewBindings.findChildViewById(rootView, id);
      if (tabLayoutPlayer == null) {
        break missingId;
      }

      id = R.id.textview_player_current_time;
      TextView textviewPlayerCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (textviewPlayerCurrentTime == null) {
        break missingId;
      }

      id = R.id.textview_player_total_time;
      TextView textviewPlayerTotalTime = ViewBindings.findChildViewById(rootView, id);
      if (textviewPlayerTotalTime == null) {
        break missingId;
      }

      id = R.id.view_pager_player;
      ViewPager2 viewPagerPlayer = ViewBindings.findChildViewById(rootView, id);
      if (viewPagerPlayer == null) {
        break missingId;
      }

      id = R.id.vinyl_background;
      ImageView vinylBackground = ViewBindings.findChildViewById(rootView, id);
      if (vinylBackground == null) {
        break missingId;
      }

      return new FragmentPlayerBinding((RelativeLayout) rootView, albumArt, albumCoverView,
          backgroundBlur, backgroundOverlay, buttonPlayerCollect, buttonPlayerComment,
          buttonPlayerIntelligence, buttonPlayerNext, buttonPlayerPlayMode, buttonPlayerPlayPause,
          buttonPlayerPlaylist, buttonPlayerPrev, buttonPlayerShare, contentContainer,
          controlContainer, loadingView, seekbarPlayerProgress, songArtist, songTitle,
          tabLayoutPlayer, textviewPlayerCurrentTime, textviewPlayerTotalTime, viewPagerPlayer,
          vinylBackground);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
