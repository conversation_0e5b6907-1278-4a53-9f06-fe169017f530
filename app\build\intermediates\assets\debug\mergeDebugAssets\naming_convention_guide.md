# 轻聆音乐播放器 - 命名规范指南

## 概述

为了提高代码的可读性、可维护性和一致性，本文档规定了轻聆音乐播放器项目的命名规范。所有开发人员都应该遵循这些规范，确保代码风格的统一。

## 1. Java 类命名规范

### 1.1 类名

- 使用 **大驼峰命名法**（UpperCamelCase）
- 名词或名词短语
- 具有描述性，表明类的用途
- 避免缩写（除非是广泛接受的缩写，如 HTTP、URL 等）

```java
// 正确示例
public class SongDetailActivity { }
public class PlaylistAdapter { }
public class MusicRepository { }

// 错误示例
public class songDetail { }  // 应该使用大驼峰命名法
public class SD { }  // 不具有描述性
```

### 1.2 接口名

- 使用 **大驼峰命名法**
- 可以使用形容词或名词
- 避免使用 "I" 前缀

```java
// 正确示例
public interface Playable { }
public interface MusicService { }

// 错误示例
public interface IPlayable { }  // 不要使用 "I" 前缀
```

### 1.3 枚举名

- 使用 **大驼峰命名法**
- 单数形式

```java
// 正确示例
public enum PlayMode { }
public enum RepeatType { }

// 错误示例
public enum playModes { }  // 应该使用大驼峰命名法和单数形式
```

## 2. 变量命名规范

### 2.1 成员变量

- 使用 **小驼峰命名法**（lowerCamelCase）
- 具有描述性
- 避免使用单个字符
- 布尔类型变量应该以 "is"、"has"、"can" 等开头

```java
// 正确示例
private String songTitle;
private boolean isPlaying;
private int songCount;

// 错误示例
private String s;  // 不具有描述性
private boolean playing;  // 布尔变量应该以 "is"、"has"、"can" 等开头
```

### 2.2 常量

- 使用 **全大写下划线分隔** 命名法（UPPER_CASE_WITH_UNDERSCORES）

```java
// 正确示例
public static final int MAX_RETRY_COUNT = 3;
public static final String DEFAULT_ENDPOINT = "https://api.example.com";

// 错误示例
public static final int maxRetryCount = 3;  // 应该使用全大写下划线分隔
```

### 2.3 参数

- 使用 **小驼峰命名法**
- 具有描述性

```java
// 正确示例
public void playSong(String songId, boolean autoPlay) { }

// 错误示例
public void playSong(String s, boolean b) { }  // 不具有描述性
```

## 3. 方法命名规范

### 3.1 一般方法

- 使用 **小驼峰命名法**
- 动词或动词短语
- 具有描述性，表明方法的用途

```java
// 正确示例
public void playSong() { }
public void stopPlayback() { }
public Song findSongById(String id) { }

// 错误示例
public void song() { }  // 应该使用动词或动词短语
public void doIt() { }  // 不具有描述性
```

### 3.2 Getter 和 Setter 方法

- 使用 **小驼峰命名法**
- Getter 方法以 "get" 开头，布尔类型可以以 "is" 开头
- Setter 方法以 "set" 开头
- 后面跟着属性名（首字母大写）

```java
// 正确示例
public String getTitle() { return title; }
public void setTitle(String title) { this.title = title; }
public boolean isPlaying() { return isPlaying; }
public void setPlaying(boolean isPlaying) { this.isPlaying = isPlaying; }

// 错误示例
public String getTitle() { return name; }  // 方法名与返回的属性不一致
public void setname(String name) { }  // 应该是 setName
```

### 3.3 布尔方法

- 使用 **小驼峰命名法**
- 以 "is"、"has"、"can" 等开头

```java
// 正确示例
public boolean isPlaying() { }
public boolean hasPermission(String permission) { }
public boolean canPlay() { }

// 错误示例
public boolean playing() { }  // 应该以 "is"、"has"、"can" 等开头
```

## 4. 包命名规范

- 使用 **全小写**
- 使用反向域名格式
- 避免使用下划线或其他特殊字符

```java
// 正确示例
package com.example.aimusicplayer.model;
package com.example.aimusicplayer.viewmodel;

// 错误示例
package com.example.AIMusicPlayer;  // 应该使用全小写
package com.example.ai_music_player;  // 避免使用下划线
```

## 5. 资源命名规范

### 5.1 布局文件

- 使用 **小写下划线分隔** 命名法（lower_case_with_underscores）
- 以元素类型为前缀

```
// 正确示例
activity_main.xml
fragment_player.xml
item_song.xml
dialog_playlist.xml

// 错误示例
MainScreen.xml  // 应该使用小写下划线分隔
player.xml  // 缺少元素类型前缀
```

### 5.2 ID

- 使用 **小写下划线分隔** 命名法
- 以元素类型为前缀

```xml
<!-- 正确示例 -->
android:id="@+id/text_song_title"
android:id="@+id/button_play"
android:id="@+id/image_album_art"

<!-- 错误示例 -->
android:id="@+id/songTitle"  <!-- 应该使用小写下划线分隔 -->
android:id="@+id/play"  <!-- 缺少元素类型前缀 -->
```

### 5.3 图标和图片

- 使用 **小写下划线分隔** 命名法
- 以功能和元素类型为前缀

```
// 正确示例
ic_play.xml
ic_pause.xml
bg_player.png
img_album_placeholder.png

// 错误示例
play.xml  // 缺少元素类型前缀
albumPlaceholder.png  // 应该使用小写下划线分隔
```

## 6. 避免使用兼容方法

为了保持代码的一致性和可维护性，我们应该避免使用兼容方法。如果确实需要兼容方法，应该遵循以下规则：

1. 使用 `@Deprecated` 注解标记兼容方法
2. 在文档中说明替代方法
3. 在适当的时候移除兼容方法

```java
/**
 * 是否已收藏（兼容方法）
 * @return 是否已收藏
 * @deprecated 使用 {@link #isFavorite()} 代替，此方法将在未来版本中移除
 */
@Deprecated
public boolean isLiked() {
    return isFavorite;
}
```

## 7. 总结

遵循一致的命名规范可以提高代码的可读性和可维护性。在轻聆音乐播放器项目中，我们应该严格遵循这些规范，确保代码风格的统一。

如果你发现代码中有不符合规范的命名，请及时修正，并确保新代码遵循这些规范。
