package com.example.aimusicplayer.ui.discovery;

import com.example.aimusicplayer.api.ApiManager;
import com.example.aimusicplayer.api.UnifiedApiService;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TopListFragment_MembersInjector implements MembersInjector<TopListFragment> {
  private final Provider<ApiManager> apiManagerProvider;

  private final Provider<UnifiedApiService> apiServiceProvider;

  public TopListFragment_MembersInjector(Provider<ApiManager> apiManagerProvider,
      Provider<UnifiedApiService> apiServiceProvider) {
    this.apiManagerProvider = apiManagerProvider;
    this.apiServiceProvider = apiServiceProvider;
  }

  public static MembersInjector<TopListFragment> create(Provider<ApiManager> apiManagerProvider,
      Provider<UnifiedApiService> apiServiceProvider) {
    return new TopListFragment_MembersInjector(apiManagerProvider, apiServiceProvider);
  }

  @Override
  public void injectMembers(TopListFragment instance) {
    injectApiManager(instance, apiManagerProvider.get());
    injectApiService(instance, apiServiceProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.discovery.TopListFragment.apiManager")
  public static void injectApiManager(TopListFragment instance, ApiManager apiManager) {
    instance.apiManager = apiManager;
  }

  @InjectedFieldSignature("com.example.aimusicplayer.ui.discovery.TopListFragment.apiService")
  public static void injectApiService(TopListFragment instance, UnifiedApiService apiService) {
    instance.apiService = apiService;
  }
}
