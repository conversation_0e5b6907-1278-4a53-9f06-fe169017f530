// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import com.example.aimusicplayer.ui.widget.AlbumCoverView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPlayerBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final AlbumCoverView albumCoverView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final ImageButton btnNext;

  @NonNull
  public final ImageButton btnPlayMode;

  @NonNull
  public final ImageButton btnPlayPause;

  @NonNull
  public final ImageButton btnPlaylist;

  @NonNull
  public final ImageButton btnPrev;

  @NonNull
  public final ImageView ivBackground;

  @NonNull
  public final SeekBar seekbarProgress;

  @NonNull
  public final ScrollView svLyrics;

  @NonNull
  public final TextView tvArtist;

  @NonNull
  public final TextView tvCurrentTime;

  @NonNull
  public final TextView tvLyrics;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvTotalTime;

  private ActivityPlayerBinding(@NonNull FrameLayout rootView,
      @NonNull AlbumCoverView albumCoverView, @NonNull ImageButton btnBack,
      @NonNull ImageButton btnNext, @NonNull ImageButton btnPlayMode,
      @NonNull ImageButton btnPlayPause, @NonNull ImageButton btnPlaylist,
      @NonNull ImageButton btnPrev, @NonNull ImageView ivBackground,
      @NonNull SeekBar seekbarProgress, @NonNull ScrollView svLyrics, @NonNull TextView tvArtist,
      @NonNull TextView tvCurrentTime, @NonNull TextView tvLyrics, @NonNull TextView tvTitle,
      @NonNull TextView tvTotalTime) {
    this.rootView = rootView;
    this.albumCoverView = albumCoverView;
    this.btnBack = btnBack;
    this.btnNext = btnNext;
    this.btnPlayMode = btnPlayMode;
    this.btnPlayPause = btnPlayPause;
    this.btnPlaylist = btnPlaylist;
    this.btnPrev = btnPrev;
    this.ivBackground = ivBackground;
    this.seekbarProgress = seekbarProgress;
    this.svLyrics = svLyrics;
    this.tvArtist = tvArtist;
    this.tvCurrentTime = tvCurrentTime;
    this.tvLyrics = tvLyrics;
    this.tvTitle = tvTitle;
    this.tvTotalTime = tvTotalTime;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPlayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPlayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_player, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPlayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.album_cover_view;
      AlbumCoverView albumCoverView = ViewBindings.findChildViewById(rootView, id);
      if (albumCoverView == null) {
        break missingId;
      }

      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_next;
      ImageButton btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btn_play_mode;
      ImageButton btnPlayMode = ViewBindings.findChildViewById(rootView, id);
      if (btnPlayMode == null) {
        break missingId;
      }

      id = R.id.btn_play_pause;
      ImageButton btnPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (btnPlayPause == null) {
        break missingId;
      }

      id = R.id.btn_playlist;
      ImageButton btnPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (btnPlaylist == null) {
        break missingId;
      }

      id = R.id.btn_prev;
      ImageButton btnPrev = ViewBindings.findChildViewById(rootView, id);
      if (btnPrev == null) {
        break missingId;
      }

      id = R.id.iv_background;
      ImageView ivBackground = ViewBindings.findChildViewById(rootView, id);
      if (ivBackground == null) {
        break missingId;
      }

      id = R.id.seekbar_progress;
      SeekBar seekbarProgress = ViewBindings.findChildViewById(rootView, id);
      if (seekbarProgress == null) {
        break missingId;
      }

      id = R.id.sv_lyrics;
      ScrollView svLyrics = ViewBindings.findChildViewById(rootView, id);
      if (svLyrics == null) {
        break missingId;
      }

      id = R.id.tv_artist;
      TextView tvArtist = ViewBindings.findChildViewById(rootView, id);
      if (tvArtist == null) {
        break missingId;
      }

      id = R.id.tv_current_time;
      TextView tvCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTime == null) {
        break missingId;
      }

      id = R.id.tv_lyrics;
      TextView tvLyrics = ViewBindings.findChildViewById(rootView, id);
      if (tvLyrics == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_total_time;
      TextView tvTotalTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalTime == null) {
        break missingId;
      }

      return new ActivityPlayerBinding((FrameLayout) rootView, albumCoverView, btnBack, btnNext,
          btnPlayMode, btnPlayPause, btnPlaylist, btnPrev, ivBackground, seekbarProgress, svLyrics,
          tvArtist, tvCurrentTime, tvLyrics, tvTitle, tvTotalTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
