package com.example.aimusicplayer.di;

import com.example.aimusicplayer.api.ApiManager;
import com.example.aimusicplayer.api.UnifiedApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideUnifiedApiServiceFactory implements Factory<UnifiedApiService> {
  private final Provider<ApiManager> apiManagerProvider;

  public AppModule_ProvideUnifiedApiServiceFactory(Provider<ApiManager> apiManagerProvider) {
    this.apiManagerProvider = apiManagerProvider;
  }

  @Override
  public UnifiedApiService get() {
    return provideUnifiedApiService(apiManagerProvider.get());
  }

  public static AppModule_ProvideUnifiedApiServiceFactory create(
      Provider<ApiManager> apiManagerProvider) {
    return new AppModule_ProvideUnifiedApiServiceFactory(apiManagerProvider);
  }

  public static UnifiedApiService provideUnifiedApiService(ApiManager apiManager) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideUnifiedApiService(apiManager));
  }
}
