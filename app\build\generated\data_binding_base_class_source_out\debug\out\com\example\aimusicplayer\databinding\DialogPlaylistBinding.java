// Generated by view binder compiler. Do not edit!
package com.example.aimusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.aimusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPlaylistBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnClearPlaylist;

  @NonNull
  public final ImageButton buttonPlaylistClose;

  @NonNull
  public final RecyclerView recyclerViewPlaylist;

  @NonNull
  public final TextView textEmptyPlaylist;

  @NonNull
  public final TextView textPlaylistCount;

  private DialogPlaylistBinding(@NonNull LinearLayout rootView, @NonNull Button btnClearPlaylist,
      @NonNull ImageButton buttonPlaylistClose, @NonNull RecyclerView recyclerViewPlaylist,
      @NonNull TextView textEmptyPlaylist, @NonNull TextView textPlaylistCount) {
    this.rootView = rootView;
    this.btnClearPlaylist = btnClearPlaylist;
    this.buttonPlaylistClose = buttonPlaylistClose;
    this.recyclerViewPlaylist = recyclerViewPlaylist;
    this.textEmptyPlaylist = textEmptyPlaylist;
    this.textPlaylistCount = textPlaylistCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPlaylistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPlaylistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_playlist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPlaylistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_clear_playlist;
      Button btnClearPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (btnClearPlaylist == null) {
        break missingId;
      }

      id = R.id.button_playlist_close;
      ImageButton buttonPlaylistClose = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlaylistClose == null) {
        break missingId;
      }

      id = R.id.recycler_view_playlist;
      RecyclerView recyclerViewPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPlaylist == null) {
        break missingId;
      }

      id = R.id.text_empty_playlist;
      TextView textEmptyPlaylist = ViewBindings.findChildViewById(rootView, id);
      if (textEmptyPlaylist == null) {
        break missingId;
      }

      id = R.id.text_playlist_count;
      TextView textPlaylistCount = ViewBindings.findChildViewById(rootView, id);
      if (textPlaylistCount == null) {
        break missingId;
      }

      return new DialogPlaylistBinding((LinearLayout) rootView, btnClearPlaylist,
          buttonPlaylistClose, recyclerViewPlaylist, textEmptyPlaylist, textPlaylistCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
